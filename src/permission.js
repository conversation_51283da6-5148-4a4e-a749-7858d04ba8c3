import router from "@/router.js";
import store from "./store/index.js";
import { checkToken } from "@/framework/api/login.js";
import { getMyMenu } from "@/framework/api/menu.js";
import {
  getToken,
  setToken,
  removeToken,
  refreshToken,
  stopRefreshToken,
} from "@/utils/auth";
import { postJson } from "@/utils/axios";
const Layout = () => import("./framework/views/layouts/index.vue");
const Without = () => import("./framework/views/without/index.vue");
const TagsLayout = () => import("./framework/views/TagsLayout/index.vue");
const RouterView = () => import("./components/RouterView/index.vue");
const IframeView = () => import("./components/IframeView/index.vue");
import { isExternal } from "@/utils/validate.js";
import settings from "@/settings.js";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import qs from "qs";
import provinceInfo from "@/assets/js/provinceInfo.js";

NProgress.configure({
  minimum: 0.1,
  easing: "ease",
  speed: 2000,
  showSpinner: false,
  trickle: false,
});
NProgress.inc(0.3);

const whiteList = ["/logon", "/home", "/401", ...(settings.whiteList ?? [])]; // no redirect whitelist
// var dynamicRoutes = [];
let toRoute = {};
let showLayout = "true";

router.beforeEach(async (to, from, next) => {
  toRoute = { ...to };
  NProgress.start();
  // set page title
  document.title = getPageTitle(to.meta.title);

  // ========== 智能体状态路由拦截管理 ==========
  handleAiAgentRouteChange(to, from);

  // determine whether the user has logged in
  let hasToken = getToken();
  const queryToken = to.query["token"];
  const isCheckToken = to.query["check"] ?? "true";
  showLayout = to.query["showLayout"] ?? "true";
  const globalUniqueID = to.query.globalUniqueID;
  if (globalUniqueID) {
    sessionStorage.setItem("globalUniqueID", globalUniqueID);
  }
  let canLogin = true;
  if (!hasToken && queryToken) {
    try {
      let token;
      let message = "";
      // 使用安全的方式验证令牌
      ({ data: token, msg: message } = await checkToken(queryToken));

      //存储已登录用户信息
      if (message !== "") {
        let userInfo = JSON.parse(message);
        store.commit("user/SET_USERINFO", userInfo);
        sessionStorage.setItem("userInfo", message);
        // setPagelogInfo();
      }

      hasToken = token;
      setToken(token);
      store.commit("user/SET_TOKEN", token);

      //启动定时刷新Token
      refreshToken();
    } catch (error) {
      console.log(error);
      canLogin = false;
    }
  }
  setPagelogInfo();

  if (hasToken && canLogin) {
    if (to.name === "Login") {
      stopRefreshToken();
      setTimeout(() => {
        removeToken();
      }, 1000);
      next();
    } else {
      if (store.getters.addRoutes && store.getters.addRoutes.length === 0) {
        try {
          const { data } = await getMyMenu(hasToken);
          let menu, permCodes;
          if (Array.isArray(data)) {
            menu = data;
          } else {
            menu = data.menu;
            permCodes = data.permCodes;
            store.commit("authority/CHANGE_LIST", permCodes);
          }
          let dynamicRoutes = menuItemsToRouter(menu);
          const home = getHome(dynamicRoutes[0]);
          dynamicRoutes.unshift({
            path: "/home",
            name: "home",
            component: Layout,
            hidden: true,
            redirect: { ...home },
          });
          dynamicRoutes.push({
            path: "*",
            name: "*",
            redirect: {
              name:
                showLayout == "true"
                  ? "layout404Index"
                  : showLayout == "tags"
                  ? "tags404Index"
                  : "exception404",
            },
            hidden: true,
          });
          store.commit("permission/SET_ROUTES", dynamicRoutes);

          // dynamically add accessible routes
          router.addRoutes(dynamicRoutes);

          // hack method to ensure that addRoutes is complete
          // set the replace: true, so the navigation will not leave a history record
          next({ ...to, replace: true });
          // routerGo(to, next);
        } catch (error) {
          console.log(error);
        }
      } else {
        next();
      }
    }
  } else {
    /* has no token*/

    if (whiteList.includes(to.path) || whiteList.includes(to.name)) {
      // in the free login whitelist, go directly
      next();
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      // next(`/logon?redirect=${to.path}`);
      // next(`/logon`);
      next(`/401`);
      NProgress.done();
    }
  }
});

router.afterEach(to => {
  NProgress.done();
  //菜单code不为空的情况下进行日志记录
  if (to.meta.path) {
    postJson("framework/log/access/add", {
      resourceId: to.meta.path,
      resourceName: to.meta.title,
      resourcePath: to.meta.url,
      requestParam:
        "query:[" +
        qs.stringify(to.query) +
        "],params:[" +
        qs.stringify(to.params) +
        "]",
    });
  }
});

router.onError(error => {
  if (error.code == "MODULE_NOT_FOUND") {
    router.replace({
      name:
        showLayout == "true"
          ? "layout404Index"
          : showLayout == "tags"
          ? "tags404Index"
          : "exception404",
      params: {
        frameTabTitle: toRoute.meta.title + " - 404",
      },
    });
  }
  NProgress.done();
});

function menuItemsToRouter(menuItems) {
  if (!Array.isArray(menuItems)) return [];
  let accessedRouters = [];
  menuItems.forEach(item => {
    const {
      resourceCode,
      resourceId,
      resourceName,
      resourceIcon,
      resourceUrl,
      resourceType,
      isMenu = null,
      isHidden,
      openTarget,
      parentId,
    } = item;
    let componentUrl = urlTrans(resourceUrl);
    const external = isExternal(componentUrl);
    const iframeview = isIframeUrl(componentUrl);
    const isTableConfig = isTableConfigUrl(componentUrl, external, iframeview);
    let querys;
    if (isTableConfig) {
      querys = componentUrl.split("?")[1];
      componentUrl = componentUrl.split("?")[0].replace(".vue", "");
    }
    const hidden = !(isMenu ?? (resourceType == "MENU" && !isHidden));
    const routeFlag = "" + (resourceCode ? resourceCode : resourceId);
    let name = routeFlag;
    if (routeFlag.indexOf("/") !== -1) {
      name = routeFlag.slice(0, routeFlag.indexOf("/"));
    }
    let routeComponet;
    if (componentUrl != "") {
      routeComponet = getComponent(componentUrl);
    }
    if (parentId == -1) {
      if (resourceCode == "backbone_toDoRepairOrder") {
        querys = `${querys}&globalUniqueID=${sessionStorage.getItem(
          "globalUniqueID"
        )}`;
      }
      // if (showLayout && openTarget !== "NewWindow") {
      let routeLayout = {
        path: "/layout" + name,
        name: "layout" + name,
        component:
          showLayout == "true"
            ? Layout
            : showLayout == "tags"
            ? TagsLayout
            : Without,
        hidden: hidden,
        meta: {
          title: resourceName,
          icon: resourceIcon,
          breadcrumb: true,
          openTarget: openTarget,
        },
        children: [
          {
            path: "" + routeFlag,
            name: "" + name,
            component: null,
            meta: {
              title: resourceName,
              icon: resourceIcon,
              url: resourceUrl,
              openTarget: openTarget,
              path: routeFlag,
              querys,
            },
          },
        ],
      };
      if (external) {
        routeLayout.children[0].path = componentUrl;
      }
      if (iframeview) {
        routeLayout.component = IframeView;
      }
      if (Array.isArray(item.childList)) {
        routeLayout.children = menuItemsToRouter(item.childList);
        routeLayout.alwaysShow = true;
      } else {
        routeLayout.meta = {};
        routeLayout.children[0].component = routeComponet;
      }
      accessedRouters.push(routeLayout);
      // } else {
      //   let route = {
      //     path: "/" + routeFlag,
      //     name: "" + name,
      //     component: () => import(`${componentUrl}`),
      //     hidden: hidden,
      //     meta: {
      //       title: resourceName,
      //       icon: resourceIcon,
      //       url: resourceUrl,
      //       openTarget: openTarget,
      //       path: routeFlag,
      //       querys,
      //     },
      //   };
      //   if (external) {
      //     route.path = componentUrl;
      //   }
      //   if (iframeview) {
      //     route.component = IframeView;
      //   }
      //   if (item.childList && item.childList.length) {
      //     route.component = RouterView;
      //     route.children = menuItemsToRouter(item.childList);
      //   }
      //   accessedRouters.push(route);
      // }
    } else {
      let route = {
        path: "" + routeFlag,
        name: "" + name,
        component: null,
        hidden: hidden,
        meta: {
          title: resourceName,
          icon: resourceIcon,
          url: resourceUrl,
          openTarget: openTarget,
          path: routeFlag,
          querys,
        },
      };
      if (external) {
        route.path = componentUrl;
      }
      if (iframeview) {
        route.component = IframeView;
      } else if (Array.isArray(item.childList) && item.childList.length) {
        route.component = RouterView;
        route.children = menuItemsToRouter(item.childList);
        route.alwaysShow = true;
      } else {
        route.component = routeComponet;
      }
      accessedRouters.push(route);
    }
  });
  return accessedRouters;
}

function urlTrans(url) {
  return url
    ?.replace?.(/^@collect\//, "plugin/collect/")
    ?.replace?.(/^@processing\//, "plugin/processing/")
    ?.replace?.(/^@visual\//, "plugin/visual/")
    ?.replace?.(/^@plugin\//, "plugin/")
    ?.replace?.(/^@components\//, "components/")
    ?.replace?.(/^@\//, "");
}

function isIframeUrl(url) {
  let res = false;
  if (url) {
    res = url.indexOf("./components/IframeView") !== -1;
  }
  return res;
}

function isTableConfigUrl(url, external, iframeview) {
  let res = false;
  if (url && !external && !iframeview) {
    url = url.replace(".vue", "");
    res = url.indexOf("?") !== -1;
  }
  return res;
}

function getPageTitle(pageTitle) {
  const title = settings.title ?? "";
  if (pageTitle && title) {
    return `${pageTitle} - ${title}`;
  }
  return `${title}`;
}

function getHome(params = { name: "exception404" }) {
  if (params.children && params.children.length > 0) {
    const childs = params.children;
    let childIndex = 0;
    for (let index = 0; index < childs.length; index++) {
      const { hidden } = childs[index];
      if (!hidden) {
        childIndex = index;
        break;
      }
    }
    return getHome(params.children[childIndex]);
  } else {
    let query = params?.meta?.querys ? qs.parse(params.meta.querys) : {};
    const { needToken = 0 } = query;
    if (needToken == 0) {
      return { name: params.name, query };
    } else {
      delete query.needToken;
      return { name: params.name, query: { ...query, token: getToken() } };
    }
  }
}

function getComponent(componentUrl) {
  /* webpackChunkName: "[request]" */
  if (/^plugin\//.test(componentUrl)) {
    return () =>
      import(
        /* webpackInclude: /(\.vue|\.lib\.js)$/i */
        `./plugin/${componentUrl.slice(7)}`
      );
  } else if (/^components\//.test(componentUrl)) {
    return () =>
      import(
        /* webpackInclude: /(\.vue|\.lib\.js)$/i */
        `./components/${componentUrl.slice(11)}`
      );
  } else if (/^framework\//.test(componentUrl)) {
    return () =>
      import(
        /* webpackInclude: /(\.vue|\.lib\.js)$/i */
        `./framework/${componentUrl.slice(10)}`
      );
  } else {
    return null;
  }
}

function setPagelogInfo() {
  const userInfo = store.getters.userInfo || {};
  const { provinceName = "", userName = "" } = JSON.parse(
    userInfo.attr2 || "{}"
  );
  if (settings.enableMonitoring && window.uam_xy_paq) {
    window.uam_xy_paq[3][1] = userName;
    const provinceCode =
      provinceInfo.find(item => provinceName.includes(item.provinceName))
        ?.provinceCode || provinceInfo[0].provinceCode;
    window.uam_xy_paq[4][1] = provinceCode;
  }
}

// ========== 智能体状态路由拦截管理 ==========
/**
 * 处理智能体状态的路由变化
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 */
function handleAiAgentRouteChange(to, from) {
  // 支持智能体的路由路径列表
  const supportedRoutePaths = [
    '/layoutbackbone_toDoRepairOrder/backbone_toDoRepairOrder',  // 工单列表页面
    // '/layoutcommon_orderDetail/common_orderDetail',              // 工单详情页面
    // '/layoutcommonProvinceOrder_detail/commonProvinceOrder_detail'
  ];

  try {
    if(store.getters.userInfo){
      const userInfo = store.getters.userInfo;
      if(userInfo.attr2){
          const attr2 = JSON.parse(userInfo.attr2);
          console.log('🤖 [路由拦截] 智能体状态管理:', {
            userInfo,
            supportedRoutePaths,
            attr2
          });
          if(attr2 && attr2.showCoreAi){
            supportedRoutePaths[1] = '/layoutcommon_orderDetail/common_orderDetail';
          }
          if(attr2 && attr2.showWirelessAi){
            supportedRoutePaths[2] = '/layoutcommonProvinceOrder_detail/commonProvinceOrder_detail';
          }
      }
        
    }
    
  } catch (error) {
    
  }

  // 检查目标路由是否支持智能体
  const isToRouteSupported = supportedRoutePaths.includes(to.path);
  const isFromRouteSupported = supportedRoutePaths.includes(from.path);

  console.log('🤖 [路由拦截] 智能体状态管理:', {
    fromPath: from.path,
    toPath: to.path,
    isFromRouteSupported,
    isToRouteSupported
  });

  // 如果目标路由不支持智能体，关闭智能体并更新用户真实意图为false
  if (!isToRouteSupported) {
    console.log('🤖 [路由拦截] 切换到不支持智能体的路由，关闭智能体并更新用户意图为false');
    // 延迟执行，确保store已经初始化
    setTimeout(() => {
      if (store && store.commit) {
        store.commit('aiAgent/SET_SHOW_AI_AGENT', false);
        store.commit('aiAgent/SET_AI_AGENT_AVAILABLE', false);
        // 关键：将用户真实意图状态也设置为false
        // store.commit('aiAgent/SET_USER_LAST_INTENTION_STATE', false);
        console.log('🤖 [路由拦截] 智能体已关闭，用户意图已更新为false');
      }
    }, 100);
  }
  // 如果从不支持的路由切换到支持智能体的路由，不做任何操作
  // 让目标页面的activated()方法处理智能体状态恢复
  else if (!isFromRouteSupported && isToRouteSupported) {
    console.log('🤖 [路由拦截] 从不支持的路由切换到支持智能体的路由，等待页面处理状态恢复');
  }
  // 如果都是支持智能体的路由间切换，不做任何操作
  // 避免跳动，让页面的activated()方法处理
  else if (isFromRouteSupported && isToRouteSupported) {
    console.log('🤖 [路由拦截] 支持智能体的路由间切换，保持当前状态');
  }
}
